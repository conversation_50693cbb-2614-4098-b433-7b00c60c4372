{"version": 3, "file": "EventEmitter.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/EventEmitter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAa,EAAC,KAAK,OAAO,EAAC,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAEpD;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;AAExC;;GAEG;AACH,MAAM,MAAM,OAAO,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,kBAAkB,CAAC,MAAM,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;IAC3E,EAAE,CAAC,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAC7E,GAAG,CAAC,GAAG,SAAS,MAAM,MAAM,EAC1B,IAAI,EAAE,GAAG,EACT,OAAO,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAC7B,IAAI,CAAC;IACR,IAAI,CAAC,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;IAKvE,WAAW,CAAC,GAAG,SAAS,MAAM,MAAM,EAClC,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAC5B,IAAI,CAAC;IACR,cAAc,CAAC,GAAG,SAAS,MAAM,MAAM,EACrC,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAC5B,IAAI,CAAC;IACR,IAAI,CAAC,GAAG,SAAS,MAAM,MAAM,EAC3B,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAC5B,IAAI,CAAC;IACR,aAAa,CAAC,KAAK,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC;IAE3C,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC;CAChD;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,CAAC,MAAM,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IACtE,MAAM,GAAG;IACP,GAAG,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC;CAC3B,CAAC;AAEJ;;;;;;;;;;;GAWG;AACH,qBAAa,YAAY,CAAC,MAAM,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CACjE,YAAW,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;;IAKzD;;;;OAIG;gBAED,OAAO,GAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAEjE;IAKH;;;;;OAKG;IACH,EAAE,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAC7C,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAChD,IAAI;IAYP;;;;;OAKG;IACH,GAAG,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAC9C,IAAI,EAAE,GAAG,EACT,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GACjD,IAAI;IAgBP;;;;;;OAMG;IACH,IAAI,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAC/C,IAAI,EAAE,GAAG,EACT,KAAK,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GACrC,OAAO;IAKV;;;;OAIG;IACH,cAAc,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EACzD,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAChD,IAAI;IAIP;;;;OAIG;IACH,WAAW,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EACtD,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAChD,IAAI;IAIP;;;;;OAKG;IACH,IAAI,CAAC,GAAG,SAAS,MAAM,kBAAkB,CAAC,MAAM,CAAC,EAC/C,IAAI,EAAE,GAAG,EACT,OAAO,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAChD,IAAI;IASP;;;;;OAKG;IACH,aAAa,CAAC,IAAI,EAAE,MAAM,kBAAkB,CAAC,MAAM,CAAC,GAAG,MAAM;IAI7D;;;;;;OAMG;IACH,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,kBAAkB,CAAC,MAAM,CAAC,GAAG,IAAI;IAQjE;;OAEG;IACH,CAAC,aAAa,CAAC,IAAI,IAAI;CAQxB;AAED;;GAEG;AACH,qBAAa,iBAAiB,CAC5B,MAAM,SAAS,kBAAkB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EACtD,IAAI,SAAS,SAAS,GAAG,SAAS,EAClC,KAAK,GAAG,OAAO;;gBAMH,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;IAO/D,CAAC,aAAa,CAAC,IAAI,IAAI;CAGxB"}