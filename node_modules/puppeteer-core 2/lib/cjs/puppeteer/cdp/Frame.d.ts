/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { Protocol } from 'devtools-protocol';
import type { CDPSession } from '../api/CDPSession.js';
import { Frame } from '../api/Frame.js';
import type { HTTPResponse } from '../api/HTTPResponse.js';
import type { WaitTimeoutOptions } from '../api/Page.js';
import { disposeSymbol } from '../util/disposable.js';
import type { DeviceRequestPrompt } from './DeviceRequestPrompt.js';
import type { FrameManager } from './FrameManager.js';
import { IsolatedWorld } from './IsolatedWorld.js';
import { type PuppeteerLifeCycleEvent } from './LifecycleWatcher.js';
import type { CdpPage } from './Page.js';
/**
 * @internal
 */
export declare class CdpFrame extends Frame {
    #private;
    _frameManager: FrameManager;
    _id: string;
    _loaderId: string;
    _lifecycleEvents: Set<string>;
    _parentId?: string;
    constructor(frameManager: FrameManager, frameId: string, parentFrameId: string | undefined, client: CDPSession);
    /**
     * This is used internally in DevTools.
     *
     * @internal
     */
    _client(): CDPSession;
    /**
     * Updates the frame ID with the new ID. This happens when the main frame is
     * replaced by a different frame.
     */
    updateId(id: string): void;
    updateClient(client: CDPSession, keepWorlds?: boolean): void;
    page(): CdpPage;
    isOOPFrame(): boolean;
    goto(url: string, options?: {
        referer?: string;
        referrerPolicy?: string;
        timeout?: number;
        waitUntil?: PuppeteerLifeCycleEvent | PuppeteerLifeCycleEvent[];
    }): Promise<HTTPResponse | null>;
    waitForNavigation(options?: {
        timeout?: number;
        waitUntil?: PuppeteerLifeCycleEvent | PuppeteerLifeCycleEvent[];
    }): Promise<HTTPResponse | null>;
    get client(): CDPSession;
    mainRealm(): IsolatedWorld;
    isolatedRealm(): IsolatedWorld;
    setContent(html: string, options?: {
        timeout?: number;
        waitUntil?: PuppeteerLifeCycleEvent | PuppeteerLifeCycleEvent[];
    }): Promise<void>;
    url(): string;
    parentFrame(): CdpFrame | null;
    childFrames(): CdpFrame[];
    waitForDevicePrompt(options?: WaitTimeoutOptions): Promise<DeviceRequestPrompt>;
    _navigated(framePayload: Protocol.Page.Frame): void;
    _navigatedWithinDocument(url: string): void;
    _onLifecycleEvent(loaderId: string, name: string): void;
    _onLoadingStopped(): void;
    _onLoadingStarted(): void;
    get detached(): boolean;
    [disposeSymbol](): void;
    exposeFunction(): never;
}
//# sourceMappingURL=Frame.d.ts.map