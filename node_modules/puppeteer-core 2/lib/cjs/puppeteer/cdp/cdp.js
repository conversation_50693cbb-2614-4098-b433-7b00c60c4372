"use strict";
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./Accessibility.js"), exports);
__exportStar(require("./AriaQueryHandler.js"), exports);
__exportStar(require("./Binding.js"), exports);
__exportStar(require("./Browser.js"), exports);
__exportStar(require("./BrowserConnector.js"), exports);
__exportStar(require("./cdp.js"), exports);
__exportStar(require("./CDPSession.js"), exports);
__exportStar(require("./ChromeTargetManager.js"), exports);
__exportStar(require("./Connection.js"), exports);
__exportStar(require("./Coverage.js"), exports);
__exportStar(require("./DeviceRequestPrompt.js"), exports);
__exportStar(require("./Dialog.js"), exports);
__exportStar(require("./ElementHandle.js"), exports);
__exportStar(require("./EmulationManager.js"), exports);
__exportStar(require("./ExecutionContext.js"), exports);
__exportStar(require("./FirefoxTargetManager.js"), exports);
__exportStar(require("./Frame.js"), exports);
__exportStar(require("./FrameManager.js"), exports);
__exportStar(require("./FrameManagerEvents.js"), exports);
__exportStar(require("./FrameTree.js"), exports);
__exportStar(require("./HTTPRequest.js"), exports);
__exportStar(require("./HTTPResponse.js"), exports);
__exportStar(require("./Input.js"), exports);
__exportStar(require("./IsolatedWorld.js"), exports);
__exportStar(require("./IsolatedWorlds.js"), exports);
__exportStar(require("./JSHandle.js"), exports);
__exportStar(require("./LifecycleWatcher.js"), exports);
__exportStar(require("./NetworkEventManager.js"), exports);
__exportStar(require("./NetworkManager.js"), exports);
__exportStar(require("./Page.js"), exports);
__exportStar(require("./PredefinedNetworkConditions.js"), exports);
__exportStar(require("./Target.js"), exports);
__exportStar(require("./TargetManager.js"), exports);
__exportStar(require("./Tracing.js"), exports);
__exportStar(require("./utils.js"), exports);
__exportStar(require("./WebWorker.js"), exports);
//# sourceMappingURL=cdp.js.map