"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AsyncIterableUtil = void 0;
/**
 * @internal
 */
class AsyncIterableUtil {
    static async *map(iterable, map) {
        for await (const value of iterable) {
            yield await map(value);
        }
    }
    static async *flatMap(iterable, map) {
        for await (const value of iterable) {
            yield* map(value);
        }
    }
    static async collect(iterable) {
        const result = [];
        for await (const value of iterable) {
            result.push(value);
        }
        return result;
    }
    static async first(iterable) {
        for await (const value of iterable) {
            return value;
        }
        return;
    }
}
exports.AsyncIterableUtil = AsyncIterableUtil;
//# sourceMappingURL=AsyncIterableUtil.js.map