{"version": 3, "file": "Realm.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Realm.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAEnE,OAAO,EAAC,YAAY,EAAiB,MAAM,2BAA2B,CAAC;AACvE,OAAO,EAAC,cAAc,EAAC,MAAM,6BAA6B,CAAC;AAE3D,OAAO,EACL,YAAY,EACZ,gBAAgB,EAChB,gCAAgC,EAChC,mBAAmB,EACnB,QAAQ,GACT,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAGtD,OAAO,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAC,qBAAqB,EAAC,MAAM,WAAW,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,YAAoC;IACxD,UAAU,CAAiB;IAEpC,GAAG,CAAU;IACb,QAAQ,CAAW;IAEnB,YAAY,UAA0B;QACpC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,MAAM;QACR,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG;YACtC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;SAC5B,CAAC;IACJ,CAAC;IAED,oBAAoB,GAAG,KAAK,EAC1B,MAA4C,EAC7B,EAAE;QACjB,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,qEAAqE;YACrE,QAAQ;YACR,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;IAEF,kBAAkB,GAAG,CAAC,MAA0C,EAAQ,EAAE;QACxE,IACE,MAAM,CAAC,IAAI,KAAK,QAAQ;YACxB,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG;YAChD,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,EACrC,CAAC;YACD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;YACxB,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC,CAAC;IAEF,UAAU,CAAC,OAAgB;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,EAAE,CAChB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,EAChD,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,EAAE,CAChB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAClD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;IAES,qBAAqB,CAAwC;IACvE,IAAI,aAAa;QACf,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACtD,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC/B,KAAK,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC5C,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAEhC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,qBAA6D,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,gCAAgC,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,YAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9B,IAAI,eAAe,CAAC;QACpB,MAAM,eAAe,GAAG,aAAa;YACnC,CAAC;YACD,CAAC,8CAAiC,CAAC;QACrC,MAAM,oBAAoB,GAAqC,aAAa;YAC1E,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC;gBACE,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;aACf,CAAC;QACN,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;gBACpD,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,GAAG,YAAY,KAAK,gBAAgB,IAAI,CAAC;YAE7C,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACxD,UAAU;gBACV,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe;gBACf,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,oBAAoB;aACrB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,mBAAmB,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC1D,mBAAmB,GAAG,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBAC9D,CAAC,CAAC,mBAAmB;gBACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;YACpD,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC5D,mBAAmB;gBACnB,SAAS,EAAE,IAAI,CAAC,MAAM;oBACpB,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,OAAO,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBAChD,CAAC,CAAC,CACH;oBACH,CAAC,CAAC,EAAE;gBACN,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe;gBACf,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,oBAAoB;aACrB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,eAAe,CAAC;QAEvC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACpD,MAAM,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,aAAa;YAClB,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;YAC7C,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,CAAC,aAAa,CAAC;QACb,IAAI,CAAC,UAAU,CAAC,GAAG,CACjB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,EAChD,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,GAAG,CACjB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAClD,IAAI,CAAC,oBAAoB,CAC1B,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAC9B,OAAgB,EAChB,MAA+B;IAE/B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACvD,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,CAAC"}