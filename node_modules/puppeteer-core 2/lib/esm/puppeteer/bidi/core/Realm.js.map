{"version": 3, "file": "Realm.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Realm.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAC1E,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAqBxE;;GAEG;IACmB,KAAK;sBAAS,YAAY;;;;;;iBAA1B,KAAM,SAAQ,WAOlC;;;YAqCA,wKAAU,OAAO,6DAGhB;YAMD,qKAAM,MAAM,6DAKX;YAMD,uLAAM,YAAY,6DAYjB;YAMD,2KAAM,QAAQ,6DAYb;;;QAtFD,oBAAoB;QACpB,OAAO,iEAAU;QACE,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,EAAE,CAAS;QACX,MAAM,CAAS;QACxB,kBAAkB;QAElB,YAAsB,EAAU,EAAE,MAAc;YAC9C,KAAK,EAAE,CAAC;YACR,oBAAoB;YACpB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,kBAAkB;QACpB,CAAC;QAES,UAAU;YAClB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5E,cAAc,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE;gBAChD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;oBAC3B,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QAED,IAAc,MAAM;YAClB,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAC,CAAC;QAC1B,CAAC;QACD,kBAAkB;QAGR,OAAO,CAAC,MAAe;YAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,MAAM,CAAC,OAAiB;YAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,YAAY,CAChB,mBAA2B,EAC3B,YAAqB,EACrB,UAA+B,EAAE;YAEjC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC9D,mBAAmB;gBACnB,YAAY;gBACZ,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAMD,KAAK,CAAC,QAAQ,CACZ,UAAkB,EAClB,YAAqB,EACrB,UAA2B,EAAE;YAE7B,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC1D,UAAU;gBACV,YAAY;gBACZ,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,yBArDC,eAAe,yBAMf,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,+BAQD,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,2BAeD,eAAe,CAAQ,KAAK,CAAC,EAAE;gBAC9B,wCAAwC;gBACxC,OAAO,KAAK,CAAC,OAAQ,CAAC;YACxB,CAAC,CAAC,GAeD,aAAa,EAAC;YACb,IAAI,CAAC,OAAO;gBACV,oFAAoF,CAAC;YACvF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE/C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SAvGmB,KAAK;AA0G3B;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,KAAK;IACpC,MAAM,CAAC,IAAI,CAAC,OAAwB,EAAE,OAAgB;QACpD,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oBAAoB;IACX,eAAe,CAAkB;IACjC,OAAO,CAAU;IAC1B,kBAAkB;IAET,QAAQ,GAGb;QACF,SAAS,EAAE,IAAI,GAAG,EAAE;QACpB,MAAM,EAAE,IAAI,GAAG,EAAE;KAClB,CAAC;IAEF,YAAoB,OAAwB,EAAE,OAAgB;QAC5D,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACd,oBAAoB;QACpB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,kBAAkB;IACpB,CAAC;IAEQ,UAAU;QACjB,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YACA,IAAY,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAE7C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,EAAC,KAAK,EAAC,EAAE,EAAE;YACtE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;IAC1D,CAAC;IAED,IAAa,MAAM;QACjB,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;IACnE,CAAC;CACF;AAUD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,KAAK;IAC7C,MAAM,CAAC,IAAI,CACT,KAAgC,EAChC,EAAU,EACV,MAAc;QAEd,MAAM,KAAK,GAAG,IAAI,oBAAoB,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QAC1D,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oBAAoB;IACX,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;IACnD,MAAM,CAAiC;IAChD,kBAAkB;IAElB,YACE,KAAgC,EAChC,EAAU,EACV,MAAc;QAEd,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjC,CAAC;IAEQ,UAAU;QACjB,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,yCAAyC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;IACnD,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,KAAK;IAC1C,MAAM,CAAC,IAAI,CACT,MAAuC,EACvC,EAAU,EACV,MAAc;QAEd,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACxD,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oBAAoB;IACX,QAAQ,GAAG,IAAI,GAAG,EAAgC,CAAC;IACnD,MAAM,CAAmB;IAClC,kBAAkB;IAElB,YACE,MAAuC,EACvC,EAAU,EACV,MAAc;QAEd,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAEQ,UAAU;QACjB,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAa,OAAO;QAClB,yCAAyC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;IACnD,CAAC;CACF"}